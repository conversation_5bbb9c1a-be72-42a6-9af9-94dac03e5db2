package com.healthdiet.aigc.service.impl;

import com.healthdiet.aigc.service.ImageStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 图片存储服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageStorageServiceImpl implements ImageStorageService {

    private final WebClient webClient;
    
    @Value("${file.upload.path}")
    private String uploadPath;
    
    @Value("${file.upload.base-url}")
    private String baseUrl;

    @Override
    public String downloadAndStoreImage(String imageUrl, String fileName) {
        log.info("开始下载图片: {}", imageUrl);
        
        try {
           // 1. 使用WebClient下载远程图片
            byte[] imageData = webClient.get()
                    .uri(imageUrl)
                    .retrieve()
                    .bodyToMono(byte[].class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
            
            if (imageData == null || imageData.length == 0) {
                throw new RuntimeException("下载的图片数据为空");
            }
            
            // 生成唯一文件名
            String uniqueFileName = generateUniqueFileName(fileName);
            
            // 确保上传目录存在
            Path uploadDir = Paths.get(uploadPath);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }
            
            // 保存图片到本地
            Path filePath = uploadDir.resolve(uniqueFileName);
            Files.write(filePath, imageData);
            
            // 生成访问URL
            String accessUrl = generateAccessUrl(uniqueFileName);
            
            log.info("图片下载并存储成功: {}", accessUrl);
            return accessUrl;
            
        } catch (IOException e) {
            log.error("保存图片文件失败", e);
            throw new RuntimeException("保存图片文件失败", e);
        } catch (Exception e) {
            log.error("下载图片失败: {}", imageUrl, e);
            throw new RuntimeException("下载图片失败", e);
        }
    }
    
    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String baseName) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        return String.format("%s_%s_%s.jpg", baseName.replaceAll("[^a-zA-Z0-9\u4e00-\u9fa5]", "_"), timestamp, uuid);
    }
    
    /**
     * 生成访问URL
     */
    private String generateAccessUrl(String fileName) {
        return baseUrl + fileName;
    }
}