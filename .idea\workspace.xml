<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="31b69bb4-3701-4ae6-97fc-7e49b9fdc42f" name="更改" comment="修复USER服务的jwt认证&#10;新增ai生成菜谱同时生成图片功能">
      <change afterPath="$PROJECT_DIR$/.vercel/project.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/实训报告.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="spring-beans.schema" />
        <option value="HTML File" />
        <option value="Interface" />
        <option value="TypeScript File" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\Develop\maven\apache-maven-3.9.8-bin\apache-maven-3.9.8" />
        <option name="localRepository" value="D:\Develop\maven\local" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\Develop\maven\apache-maven-3.9.8-bin\apache-maven-3.9.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="ProjectErrors" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2zzKKOuzEbH8uEC5pnN5FXRMiTv" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_ADD_EXTERNAL_FILES&quot;: &quot;true&quot;,
    &quot;Batch.start-complete-platform.executor&quot;: &quot;Run&quot;,
    &quot;DefaultHtmlFileTemplate&quot;: &quot;HTML File&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/idea/healthy-diet-platform&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.57931036&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Users\\<USER>\\Desktop\\idea\\healthy-diet-platform\\health-frontend\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\idea\healthy-diet-platform" />
      <recent name="C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-frontend\dist" />
      <recent name="C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-frontend\dist\images" />
      <recent name="C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-frontend\public" />
      <recent name="C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\src\main\resources" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-frontend\src\components\Layout" />
      <recent name="C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-frontend" />
      <recent name="C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-frontend\dist" />
      <recent name="C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\admin-service\src\main\java\com\healthdiet\admin\service" />
      <recent name="C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Batch.start-complete-platform">
    <configuration name="start-complete-platform" type="BatchConfigurationType" factoryName="Batch" temporary="true">
      <module name="healthy-diet-platform" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SCRIPT_NAME" value="start-complete-platform.bat" />
      <option name="PARAMETERS" value="" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AdminServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="admin-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.healthdiet.admin.AdminServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AigcApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="aigc-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.healthdiet.aigc.AigcApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="gateway-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.healthdiet.gateway.GatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PlanServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="plan-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.healthdiet.plan.PlanServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RecipeApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="recipe-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.healthdiet.recipe.RecipeApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="user-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.healthdiet.user.UserApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Batch.start-complete-platform" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.17890.1" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.17890.1" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="31b69bb4-3701-4ae6-97fc-7e49b9fdc42f" name="更改" comment="" />
      <created>1752728397954</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752728397954</updated>
      <workItem from="1752728399303" duration="97000" />
      <workItem from="1752728515005" duration="276000" />
      <workItem from="1752729010907" duration="280000" />
      <workItem from="1752729321375" duration="31000" />
      <workItem from="1752729355969" duration="14150000" />
      <workItem from="1752800701823" duration="21231000" />
      <workItem from="1752886921434" duration="15783000" />
      <workItem from="1753232029816" duration="74000" />
      <workItem from="1753232110388" duration="13658000" />
      <workItem from="1753335497180" duration="1517000" />
      <workItem from="1753337128657" duration="1572000" />
      <workItem from="1753340206752" duration="925000" />
      <workItem from="1753341151351" duration="4267000" />
      <workItem from="1753493724838" duration="701000" />
      <workItem from="1753616778972" duration="228000" />
      <workItem from="1753621490050" duration="736000" />
    </task>
    <task id="LOCAL-00001" summary="初始化">
      <option name="closed" value="true" />
      <created>1752729516839</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752729516839</updated>
    </task>
    <task id="LOCAL-00002" summary="初始化">
      <option name="closed" value="true" />
      <created>1752729975668</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752729975668</updated>
    </task>
    <task id="LOCAL-00003" summary="修复软件包名称">
      <option name="closed" value="true" />
      <created>1752734298775</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752734298775</updated>
    </task>
    <task id="LOCAL-00004" summary="admin服务的service以及controller">
      <option name="closed" value="true" />
      <created>1752736221480</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752736221481</updated>
    </task>
    <task id="LOCAL-00005" summary="1.完善admin服务&#10;2.完善conmmon服务&#10;3.完善auth服务&#10;4.解决fastjson2依赖问题">
      <option name="closed" value="true" />
      <created>1752806061076</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752806061076</updated>
    </task>
    <task id="LOCAL-00006" summary="解决common服务中pom的fastjson依赖问题">
      <option name="closed" value="true" />
      <created>1752807061383</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752807061383</updated>
    </task>
    <task id="LOCAL-00007" summary="部分前端">
      <option name="closed" value="true" />
      <created>1752903108497</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752903108497</updated>
    </task>
    <task id="LOCAL-00008" summary="common服务添加Jwt&#10;aigc服务">
      <option name="closed" value="true" />
      <created>1752913667015</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752913667015</updated>
    </task>
    <task id="LOCAL-00009" summary="删除错误的recipe服务下的resource文件夹">
      <option name="closed" value="true" />
      <created>1753233190138</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753233190138</updated>
    </task>
    <task id="LOCAL-00010" summary="修复错误的recipe服务下的resource文件夹">
      <option name="closed" value="true" />
      <created>1753233463029</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753233463029</updated>
    </task>
    <task id="LOCAL-00011" summary="修复错误的plan服务下的resource文件夹">
      <option name="closed" value="true" />
      <created>1753233622870</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753233622870</updated>
    </task>
    <task id="LOCAL-00012" summary="修复后端小问题">
      <option name="closed" value="true" />
      <created>1753255291139</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753255291139</updated>
    </task>
    <task id="LOCAL-00013" summary="管理员前端界面">
      <option name="closed" value="true" />
      <created>1753263244006</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753263244006</updated>
    </task>
    <task id="LOCAL-00014" summary="完善用户前端以及添加启动脚本">
      <option name="closed" value="true" />
      <created>1753336553575</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753336553575</updated>
    </task>
    <task id="LOCAL-00015" summary="修复后端服务错误">
      <option name="closed" value="true" />
      <created>1753341676399</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1753341676399</updated>
    </task>
    <task id="LOCAL-00016" summary="修复个人档案修改为女时会报错的问题">
      <option name="closed" value="true" />
      <created>1753493772490</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1753493772490</updated>
    </task>
    <task id="LOCAL-00017" summary="修复USER服务的jwt认证&#10;新增ai生成菜谱同时生成图片功能">
      <option name="closed" value="true" />
      <created>1753621568485</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1753621568485</updated>
    </task>
    <task id="LOCAL-00018" summary="修复USER服务的jwt认证&#10;新增ai生成菜谱同时生成图片功能">
      <option name="closed" value="true" />
      <created>1753621611165</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1753621611165</updated>
    </task>
    <option name="localTasksCounter" value="19" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <ignored-roots>
      <path value="$PROJECT_DIR$/health-backend" />
    </ignored-roots>
    <MESSAGE value="初始化" />
    <MESSAGE value="修复软件包名称" />
    <MESSAGE value="admin服务的service以及controller" />
    <MESSAGE value="1.完善admin服务&#10;2.完善conmmon服务&#10;3.完善auth服务&#10;4.解决fastjson2依赖问题" />
    <MESSAGE value="解决common服务中pom的fastjson依赖问题" />
    <MESSAGE value="部分前端" />
    <MESSAGE value="common服务添加Jwt&#10;aigc服务" />
    <MESSAGE value="删除错误的recipe服务下的resource文件夹" />
    <MESSAGE value="修复错误的recipe服务下的resource文件夹" />
    <MESSAGE value="修复错误的plan服务下的resource文件夹" />
    <MESSAGE value="修复后端小问题" />
    <MESSAGE value="管理员前端界面" />
    <MESSAGE value="完善用户前端以及添加启动脚本" />
    <MESSAGE value="修复后端服务错误" />
    <MESSAGE value="修复个人档案修改为女时会报错的问题" />
    <MESSAGE value="修复USER服务的jwt认证&#10;新增ai生成菜谱同时生成图片功能" />
    <option name="LAST_COMMIT_MESSAGE" value="修复USER服务的jwt认证&#10;新增ai生成菜谱同时生成图片功能" />
  </component>
</project>