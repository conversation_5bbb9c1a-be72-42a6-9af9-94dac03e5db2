package com.healthdiet.auth.controller;

import com.healthdiet.auth.dto.SendCodeRequest;
import com.healthdiet.auth.dto.ResetPasswordRequest;
import com.healthdiet.auth.service.AuthService;
import com.healthdiet.auth.service.VerificationCodeService;
import com.healthdiet.common.result.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 验证码控制器
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class  VerificationController {

    private final AuthService authService;
    private final VerificationCodeService verificationCodeService;

    /**
     * 发送注册验证码
     */
    @PostMapping("/send-register-code")
    public Result<Void> sendRegisterCode(@Valid @RequestBody SendCodeRequest request) {
        try {
            authService.sendRegisterCode(request);
            return Result.success("验证码发送成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 发送密码重置验证码
     */
    @PostMapping("/send-reset-code")
    public Result<Void> sendPasswordResetCode(@Valid @RequestBody SendCodeRequest request) {
        try {
            authService.sendPasswordResetCode(request);
            return Result.success("验证码发送成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 重置密码
     */
    @PostMapping("/reset-password")
    public Result<Void> resetPassword(@Valid @RequestBody ResetPasswordRequest request) {
        try {
            authService.resetPassword(request);
            return Result.success("密码重置成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 生成图形验证码
     */
    @GetMapping("/captcha")
    public Result<Map<String, String>> generateCaptcha(HttpServletRequest request) {
        try {
            String sessionId = UUID.randomUUID().toString();
            String captchaImage = verificationCodeService.generateCaptcha(sessionId);

            Map<String, String> result = new HashMap<>();
            result.put("sessionId", sessionId);
            result.put("captchaImage", captchaImage);

            return Result.success("验证码生成成功", result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}
