package com.healthdiet.recipe.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.healthdiet.recipe.dto.AiRecipeData;
import com.healthdiet.recipe.dto.RecipeListResponse;
import com.healthdiet.recipe.dto.RecipeRequest;
import com.healthdiet.recipe.dto.RecipeResponse;
import com.healthdiet.recipe.entity.*;
import com.healthdiet.recipe.mapper.*;
import com.healthdiet.recipe.service.NutritionService;
import com.healthdiet.recipe.service.RecipeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜谱服务实现类
 */
@Service
@RequiredArgsConstructor
public class RecipeServiceImpl implements RecipeService {

    private final RecipeMapper recipeMapper;
    private final RecipeIngredientMapper recipeIngredientMapper;
    private final RecipeStepMapper recipeStepMapper;
    private final FavoriteMapper favoriteMapper;
    private final IngredientMapper ingredientMapper;
    private final NutritionService nutritionService;

    @Override
    @Transactional
    public Long createRecipe(Long userId, RecipeRequest request) {
        // 创建菜谱基本信息
        Recipe recipe = new Recipe();
        recipe.setUserId(userId);
        recipe.setTitle(request.getTitle());
        recipe.setDescription(request.getDescription());
        recipe.setCoverImageUrl(request.getCoverImageUrl());
        recipe.setIsAiGenerated(false);

        // 计算估算热量
        int estimatedCalories = nutritionService.calculateCaloriesFromIngredients(request.getIngredients());
        recipe.setEstimatedCalories(estimatedCalories);

        recipeMapper.insert(recipe);

        // 保存食材信息
        saveRecipeIngredients(recipe.getId(), request.getIngredients());

        // 保存制作步骤
        saveRecipeSteps(recipe.getId(), request.getSteps());

        return recipe.getId();
    }

    @Override
    @Transactional
    public void updateRecipe(Long recipeId, Long userId, RecipeRequest request) {
        // 检查菜谱是否存在且属于当前用户
        Recipe recipe = recipeMapper.selectById(recipeId);
        if (recipe == null) {
            throw new RuntimeException("菜谱不存在");
        }
        if (!recipe.getUserId().equals(userId)) {
            throw new RuntimeException("无权限修改此菜谱");
        }

        // 更新菜谱基本信息
        recipe.setTitle(request.getTitle());
        recipe.setDescription(request.getDescription());
        recipe.setCoverImageUrl(request.getCoverImageUrl());

        // 重新计算估算热量
        int estimatedCalories = nutritionService.calculateCaloriesFromIngredients(request.getIngredients());
        recipe.setEstimatedCalories(estimatedCalories);

        recipeMapper.updateById(recipe);

        // 删除原有食材和步骤
        deleteRecipeIngredients(recipeId);
        deleteRecipeSteps(recipeId);

        // 保存新的食材和步骤
        saveRecipeIngredients(recipeId, request.getIngredients());
        saveRecipeSteps(recipeId, request.getSteps());
    }

    @Override
    @Transactional
    public void deleteRecipe(Long recipeId, Long userId) {
        // 检查菜谱是否存在且属于当前用户
        Recipe recipe = recipeMapper.selectById(recipeId);
        if (recipe == null) {
            throw new RuntimeException("菜谱不存在");
        }
        if (!recipe.getUserId().equals(userId)) {
            throw new RuntimeException("无权限删除此菜谱");
        }

        // 删除相关数据
        deleteRecipeIngredients(recipeId);
        deleteRecipeSteps(recipeId);
        deleteFavorites(recipeId);

        // 删除菜谱
        recipeMapper.deleteById(recipeId);
    }

    @Override
    public RecipeResponse getRecipeDetail(Long recipeId, Long currentUserId) {
        Recipe recipe = recipeMapper.selectById(recipeId);
        if (recipe == null) {
            throw new RuntimeException("菜谱不存在");
        }

        RecipeResponse response = new RecipeResponse();
        BeanUtils.copyProperties(recipe, response);

        // 检查是否已收藏
        if (currentUserId != null) {
            boolean isFavorited = checkIsFavorited(recipeId, currentUserId);
            response.setIsFavorited(isFavorited);
        }

        // 获取食材列表
        List<RecipeIngredient> ingredients = getRecipeIngredientsPrivate(recipeId);
        List<RecipeResponse.IngredientItem> ingredientItems = ingredients.stream()
                .map(ingredient -> {
                    RecipeResponse.IngredientItem item = new RecipeResponse.IngredientItem();
                    item.setId(ingredient.getId());
                    item.setName(ingredient.getIngredientName());
                    item.setQuantity(ingredient.getQuantity());
                    return item;
                })
                .collect(Collectors.toList());
        response.setIngredients(ingredientItems);

        // 获取制作步骤
        List<RecipeStep> steps = getRecipeSteps(recipeId);
        List<RecipeResponse.StepItem> stepItems = steps.stream()
                .map(step -> {
                    RecipeResponse.StepItem item = new RecipeResponse.StepItem();
                    item.setId(step.getId());
                    item.setStepNumber(step.getStepNumber());
                    item.setDescription(step.getDescription());
                    item.setImageUrl(step.getImageUrl());
                    return item;
                })
                .collect(Collectors.toList());
        response.setSteps(stepItems);

        return response;
    }

    @Override
    public IPage<RecipeListResponse> getRecipeList(Long currentPage, Long pageSize, String keyword, Long currentUserId) {
        Page<Recipe> page = new Page<>(currentPage, pageSize);
        IPage<Recipe> recipePage = recipeMapper.searchRecipes(page, keyword, currentUserId);

        return recipePage.convert(recipe -> convertToListResponse(recipe, currentUserId));
    }

    @Override
    public IPage<RecipeListResponse> searchAllRecipes(Long currentPage, Long pageSize, String keyword, Long currentUserId) {
        // 1. 创建分页对象
        Page<Recipe> page = new Page<>(currentPage, pageSize);
        // 2. 调用自定义分页查询方法
        IPage<Recipe> recipePage = recipeMapper.searchRecipes(page, keyword, null);
        // 3. 转换数据格式
        return recipePage.convert(recipe -> convertToListResponse(recipe, currentUserId));
    }

    @Override
    public IPage<RecipeListResponse> getUserRecipes(Long userId, Long currentPage, Long pageSize, Long currentUserId) {
        Page<Recipe> page = new Page<>(currentPage, pageSize);
        LambdaQueryWrapper<Recipe> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Recipe::getUserId, userId)
                .orderByDesc(Recipe::getCreatedAt);

        IPage<Recipe> recipePage = recipeMapper.selectPage(page, queryWrapper);
        return recipePage.convert(recipe -> convertToListResponse(recipe, currentUserId));
    }

    @Override
    @Transactional
    public void toggleFavorite(Long recipeId, Long userId) {
        // 检查菜谱是否存在
        Recipe recipe = recipeMapper.selectById(recipeId);
        if (recipe == null) {
            throw new RuntimeException("菜谱不存在");
        }

        // 检查是否已收藏
        LambdaQueryWrapper<Favorite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Favorite::getUserId, userId)
                .eq(Favorite::getRecipeId, recipeId);

        Favorite existingFavorite = favoriteMapper.selectOne(queryWrapper);

        if (existingFavorite != null) {
            // 已收藏，取消收藏
            favoriteMapper.delete(queryWrapper);
        } else {
            // 未收藏，添加收藏
            Favorite favorite = new Favorite();
            favorite.setUserId(userId);
            favorite.setRecipeId(recipeId);
            favoriteMapper.insert(favorite);
        }
    }

    @Override
    @Transactional
    public void addFavorite(Long recipeId, Long userId) {
        // 检查菜谱是否存在
        Recipe recipe = recipeMapper.selectById(recipeId);
        if (recipe == null) {
            throw new RuntimeException("菜谱不存在");
        }

        // 检查是否已收藏
        LambdaQueryWrapper<Favorite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Favorite::getUserId, userId)
                .eq(Favorite::getRecipeId, recipeId);

        Favorite existingFavorite = favoriteMapper.selectOne(queryWrapper);
        if (existingFavorite != null) {
            throw new RuntimeException("已经收藏过该菜谱");
        }

        // 添加收藏
        Favorite favorite = new Favorite();
        favorite.setUserId(userId);
        favorite.setRecipeId(recipeId);
        favoriteMapper.insert(favorite);
    }

    @Override
    @Transactional
    public void removeFavorite(Long recipeId, Long userId) {
        // 检查是否已收藏
        LambdaQueryWrapper<Favorite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Favorite::getUserId, userId)
                .eq(Favorite::getRecipeId, recipeId);

        Favorite existingFavorite = favoriteMapper.selectOne(queryWrapper);
        if (existingFavorite == null) {
            throw new RuntimeException("尚未收藏该菜谱");
        }

        // 取消收藏
        favoriteMapper.delete(queryWrapper);
    }

    @Override
    public IPage<RecipeListResponse> getFavoriteRecipes(Long userId, Long currentPage, Long pageSize) {
        Page<Recipe> page = new Page<>(currentPage, pageSize);
        IPage<Recipe> recipePage = recipeMapper.getFavoriteRecipes(page, userId);

        return recipePage.convert(recipe -> convertToListResponse(recipe, userId));
    }

    @Override
    @Transactional
    public Long createAiRecipe(Long userId, AiRecipeData aiRecipeData) {
        // 创建菜谱基本信息
        Recipe recipe = new Recipe();
        recipe.setUserId(userId);
        recipe.setTitle(aiRecipeData.getTitle());
        recipe.setDescription(aiRecipeData.getDescription());
        recipe.setCookingTime(aiRecipeData.getCookingTime() != null ? aiRecipeData.getCookingTime() : 30);
        recipe.setDifficulty(aiRecipeData.getDifficulty() != null ? aiRecipeData.getDifficulty() : 1);
        recipe.setCoverImageUrl(aiRecipeData.getImageUrl());
        recipe.setIsAiGenerated(true);

        // 转换食材数据并计算热量
        List<RecipeRequest.IngredientItem> ingredientItems = aiRecipeData.getIngredients().stream()
                .map(ai -> {
                    RecipeRequest.IngredientItem item = new RecipeRequest.IngredientItem();
                    item.setName(ai.getName());
                    item.setQuantity(ai.getQuantity());
                    return item;
                })
                .collect(Collectors.toList());

        int estimatedCalories = nutritionService.calculateCaloriesFromIngredients(ingredientItems);
        recipe.setEstimatedCalories(estimatedCalories);

        recipeMapper.insert(recipe);

        // 保存食材信息
        saveAiRecipeIngredients(recipe.getId(), aiRecipeData.getIngredients());

        // 保存制作步骤
        saveAiRecipeSteps(recipe.getId(), aiRecipeData.getSteps());

        return recipe.getId();
    }

    @Override
    public List<com.healthdiet.recipe.controller.RecipeController.RecipeIngredientResponse> getRecipeIngredients(Long recipeId) {
        LambdaQueryWrapper<RecipeIngredient> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RecipeIngredient::getRecipeId, recipeId);
        List<RecipeIngredient> ingredients = recipeIngredientMapper.selectList(queryWrapper);

        return ingredients.stream()
                .map(ingredient -> new com.healthdiet.recipe.controller.RecipeController.RecipeIngredientResponse(
                        ingredient.getIngredientName(),
                        ingredient.getQuantity()))
                .collect(Collectors.toList());
    }

    /**
     * 保存菜谱食材
     */
    private void saveRecipeIngredients(Long recipeId, List<RecipeRequest.IngredientItem> ingredients) {
        for (RecipeRequest.IngredientItem ingredientItem : ingredients) {
            RecipeIngredient recipeIngredient = new RecipeIngredient();
            recipeIngredient.setRecipeId(recipeId);
            recipeIngredient.setIngredientName(ingredientItem.getName());
            recipeIngredient.setQuantity(ingredientItem.getQuantity());
            recipeIngredientMapper.insert(recipeIngredient);
        }
    }

    /**
     * 保存菜谱步骤
     */
    private void saveRecipeSteps(Long recipeId, List<RecipeRequest.StepItem> steps) {
        for (int i = 0; i < steps.size(); i++) {
            RecipeRequest.StepItem stepItem = steps.get(i);
            RecipeStep recipeStep = new RecipeStep();
            recipeStep.setRecipeId(recipeId);
            recipeStep.setStepNumber(i + 1);
            recipeStep.setDescription(stepItem.getDescription());
            recipeStep.setImageUrl(stepItem.getImageUrl());
            recipeStepMapper.insert(recipeStep);
        }
    }

    /**
     * 删除菜谱食材
     */
    private void deleteRecipeIngredients(Long recipeId) {
        LambdaQueryWrapper<RecipeIngredient> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RecipeIngredient::getRecipeId, recipeId);
        recipeIngredientMapper.delete(queryWrapper);
    }

    /**
     * 删除菜谱步骤
     */
    private void deleteRecipeSteps(Long recipeId) {
        LambdaQueryWrapper<RecipeStep> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RecipeStep::getRecipeId, recipeId);
        recipeStepMapper.delete(queryWrapper);
    }

    /**
     * 删除收藏记录
     */
    private void deleteFavorites(Long recipeId) {
        LambdaQueryWrapper<Favorite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Favorite::getRecipeId, recipeId);
        favoriteMapper.delete(queryWrapper);
    }

    /**
     * 检查是否已收藏
     */
    private boolean checkIsFavorited(Long recipeId, Long userId) {
        LambdaQueryWrapper<Favorite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Favorite::getUserId, userId)
                .eq(Favorite::getRecipeId, recipeId);
        return favoriteMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 获取菜谱食材（私有方法）
     */
    private List<RecipeIngredient> getRecipeIngredientsPrivate(Long recipeId) {
        LambdaQueryWrapper<RecipeIngredient> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RecipeIngredient::getRecipeId, recipeId);
        return recipeIngredientMapper.selectList(queryWrapper);
    }

    /**
     * 获取菜谱步骤
     */
    private List<RecipeStep> getRecipeSteps(Long recipeId) {
        LambdaQueryWrapper<RecipeStep> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RecipeStep::getRecipeId, recipeId)
                .orderByAsc(RecipeStep::getStepNumber);
        return recipeStepMapper.selectList(queryWrapper);
    }

    /**
     * 转换为列表响应DTO
     */
    private RecipeListResponse convertToListResponse(Recipe recipe, Long currentUserId) {
        RecipeListResponse response = new RecipeListResponse();
        BeanUtils.copyProperties(recipe, response);

        // 设置用户名：如果是AI生成，显示"AI"，否则显示用户名
        if (Boolean.TRUE.equals(recipe.getIsAiGenerated())) {
            response.setUsername("AI");
        } else {
            response.setUsername(recipe.getUsername());
        }

        // 检查是否已收藏
        if (currentUserId != null) {
            boolean isFavorited = checkIsFavorited(recipe.getId(), currentUserId);
            response.setIsFavorited(isFavorited);
        }

        return response;
    }

    /**
     * 保存AI菜谱食材
     */
    private void saveAiRecipeIngredients(Long recipeId, List<AiRecipeData.IngredientItem> ingredients) {
        for (AiRecipeData.IngredientItem ingredientItem : ingredients) {
            RecipeIngredient recipeIngredient = new RecipeIngredient();
            recipeIngredient.setRecipeId(recipeId);
            recipeIngredient.setIngredientName(ingredientItem.getName());
            recipeIngredient.setQuantity(ingredientItem.getQuantity());
            recipeIngredientMapper.insert(recipeIngredient);
        }
    }

    /**
     * 保存AI菜谱步骤
     */
    private void saveAiRecipeSteps(Long recipeId, List<AiRecipeData.StepItem> steps) {
        for (int i = 0; i < steps.size(); i++) {
            AiRecipeData.StepItem stepItem = steps.get(i);
            RecipeStep recipeStep = new RecipeStep();
            recipeStep.setRecipeId(recipeId);
            recipeStep.setStepNumber(i + 1);
            recipeStep.setDescription(stepItem.getDescription());
            recipeStepMapper.insert(recipeStep);
        }
    }
}
