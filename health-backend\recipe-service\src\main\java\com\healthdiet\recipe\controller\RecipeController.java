package com.healthdiet.recipe.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.healthdiet.common.result.PageResult;
import com.healthdiet.common.result.Result;
import com.healthdiet.common.result.PageResult;
import com.healthdiet.recipe.dto.AiRecipeData;
import com.healthdiet.recipe.dto.RecipeListResponse;
import com.healthdiet.recipe.dto.RecipeRequest;
import com.healthdiet.recipe.dto.RecipeResponse;
import com.healthdiet.recipe.entity.Recipe;
import com.healthdiet.recipe.mapper.RecipeMapper;
import com.healthdiet.recipe.service.RecipeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 菜谱控制器
 */
@RestController
@RequestMapping("/recipes")
@RequiredArgsConstructor
public class RecipeController {

    private final RecipeService recipeService;
    private final RecipeMapper recipeMapper;
    
    /**
     * 创建菜谱
     */
    @PostMapping
    public Result<Long> createRecipe(
            @RequestHeader("X-User-Id") Long userId,
            @Valid @RequestBody RecipeRequest request) {
        try {
            Long recipeId = recipeService.createRecipe(userId, request);
            return Result.success("菜谱创建成功", recipeId);
        } catch (Exception e) {
            e.printStackTrace(); // 添加异常堆栈打印
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 更新菜谱
     */
    @PutMapping("/{recipeId}")
    public Result<Void> updateRecipe(
            @PathVariable Long recipeId,
            @RequestHeader("X-User-Id") Long userId,
            @Valid @RequestBody RecipeRequest request) {
        try {
            recipeService.updateRecipe(recipeId, userId, request);
            return Result.success("菜谱更新成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 删除菜谱
     */
    @DeleteMapping("/{recipeId}")
    public Result<Void> deleteRecipe(
            @PathVariable Long recipeId,
            @RequestHeader("X-User-Id") Long userId) {
        try {
            recipeService.deleteRecipe(recipeId, userId);
            return Result.success("菜谱删除成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取菜谱详情
     */
    @GetMapping("/{recipeId}")
    public Result<RecipeResponse> getRecipeDetail(
            @PathVariable Long recipeId,
            @RequestHeader(value = "X-User-Id", required = false) Long currentUserId) {
        try {
            RecipeResponse response = recipeService.getRecipeDetail(recipeId, currentUserId);
            return Result.success(response);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 分页获取菜谱列表
     */
    @GetMapping
    public Result<PageResult<RecipeListResponse>> getRecipeList(
            @RequestParam(value = "currentPage", defaultValue = "1") Long currentPage,
            @RequestParam(value = "pageSize", defaultValue = "10") Long pageSize,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestHeader(value = "X-User-Id", required = false) Long currentUserId) {
        try {
            IPage<RecipeListResponse> page = recipeService.getRecipeList(currentPage, pageSize, keyword, currentUserId);
            PageResult<RecipeListResponse> result = new PageResult<>(
                page.getRecords(), page.getTotal(), page.getCurrent(), page.getSize());
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 搜索菜谱 (前端期望的搜索接口)
     */
    @GetMapping("/search")
    public Result<PageResult<RecipeListResponse>> searchRecipes(
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "size", defaultValue = "10") Integer size,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "category", required = false) String category,
            @RequestParam(value = "difficulty", required = false) String difficulty,
            @RequestHeader(value = "X-User-Id", required = false) Long currentUserId) {
        try {
            // 转换分页参数 (前端使用0基础，后端使用1基础)
            Long currentPage = (long) (page + 1);
            Long pageSize = (long) size;

            // 使用公共搜索方法，搜索所有用户的菜谱
            IPage<RecipeListResponse> pageResult = recipeService.searchAllRecipes(currentPage, pageSize, keyword, currentUserId);

            // 转换为前端期望的格式
            PageResult<RecipeListResponse> result = new PageResult<>(
                pageResult.getRecords(),
                pageResult.getTotal(),
                (long) page,
                (long) size
            );

            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户发布的菜谱
     */
    @GetMapping("/user/{userId}")
    public Result<PageResult<RecipeListResponse>> getUserRecipes(
            @PathVariable Long userId,
            @RequestParam(value = "currentPage", defaultValue = "1") Long currentPage,
            @RequestParam(value = "pageSize", defaultValue = "10") Long pageSize,
            @RequestHeader(value = "X-User-Id", required = false) Long currentUserId) {
        try {
            IPage<RecipeListResponse> page = recipeService.getUserRecipes(userId, currentPage, pageSize, currentUserId);
            PageResult<RecipeListResponse> result = new PageResult<>(
                page.getRecords(), page.getTotal(), page.getCurrent(), page.getSize());
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 收藏菜谱
     */
    @PostMapping("/{recipeId}/favorite")
    public Result<Void> favoriteRecipe(
            @PathVariable Long recipeId,
            @RequestHeader("X-User-Id") Long userId) {
        try {
            recipeService.addFavorite(recipeId, userId);
            return Result.success("收藏成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 取消收藏菜谱
     */
    @DeleteMapping("/{recipeId}/favorite")
    public Result<Void> unfavoriteRecipe(
            @PathVariable Long recipeId,
            @RequestHeader("X-User-Id") Long userId) {
        try {
            recipeService.removeFavorite(recipeId, userId);
            return Result.success("取消收藏成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户收藏的菜谱
     */
    @GetMapping("/favorites")
    public Result<PageResult<RecipeListResponse>> getFavoriteRecipes(
            @RequestHeader("X-User-Id") Long userId,
            @RequestParam(value = "currentPage", defaultValue = "1") Long currentPage,
            @RequestParam(value = "pageSize", defaultValue = "10") Long pageSize) {
        try {
            IPage<RecipeListResponse> page = recipeService.getFavoriteRecipes(userId, currentPage, pageSize);
            PageResult<RecipeListResponse> result = new PageResult<>(
                page.getRecords(), page.getTotal(), page.getCurrent(), page.getSize());
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 创建AI生成的菜谱
     */
    @PostMapping("/ai")
    public Result<Long> createAiRecipe(
            @RequestHeader("X-User-Id") Long userId,
            @RequestBody AiRecipeData aiRecipeData) {
        try {
            Long recipeId = recipeService.createAiRecipe(userId, aiRecipeData);
            return Result.success("AI菜谱创建成功", recipeId);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取菜谱食材列表
     */
    @GetMapping("/{recipeId}/ingredients")
    public Result<java.util.List<RecipeIngredientResponse>> getRecipeIngredients(@PathVariable Long recipeId) {
        try {
            java.util.List<RecipeIngredientResponse> ingredients = recipeService.getRecipeIngredients(recipeId);
            return Result.success(ingredients);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }





    /**
     * 上传图片
     */
    @PostMapping("/upload")
    public Result<String> uploadImage(
            @RequestHeader("X-User-Id") Long userId,
            @RequestParam("file") org.springframework.web.multipart.MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return Result.error("文件不能为空");
            }

            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.error("只能上传图片文件");
            }

            // 验证文件大小 (5MB)
            if (file.getSize() > 5 * 1024 * 1024) {
                return Result.error("文件大小不能超过5MB");
            }

            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename != null && originalFilename.contains(".")
                ? originalFilename.substring(originalFilename.lastIndexOf("."))
                : ".jpg";
            String filename = "recipe_" + System.currentTimeMillis() + extension;

            // 创建上传目录 - 使用绝对路径
            String uploadDir = System.getProperty("user.dir") + "/uploads/recipes/";
            java.io.File dir = new java.io.File(uploadDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                System.out.println("创建目录: " + uploadDir + ", 结果: " + created);
            }

            // 保存文件
            String filePath = uploadDir + filename;
            java.io.File dest = new java.io.File(filePath);
            System.out.println("保存文件到: " + filePath);
            file.transferTo(dest);

            // 生成访问URL - 使用完整的服务路径
            String imageUrl = "http://localhost:8083/uploads/recipes/" + filename;

            return Result.success("图片上传成功", imageUrl);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 菜谱食材响应DTO
     */
    public static class RecipeIngredientResponse {
        private String ingredientName;
        private String quantity;

        public RecipeIngredientResponse() {}

        public RecipeIngredientResponse(String ingredientName, String quantity) {
            this.ingredientName = ingredientName;
            this.quantity = quantity;
        }

        // Getters and Setters
        public String getIngredientName() { return ingredientName; }
        public void setIngredientName(String ingredientName) { this.ingredientName = ingredientName; }

        public String getQuantity() { return quantity; }
        public void setQuantity(String quantity) { this.quantity = quantity; }
    }

    // ==================== 管理员接口 ====================

    /**
     * 获取菜谱总数 (供admin-service调用)
     */
    @GetMapping("/count")
    public Result<Long> getRecipeCount() {
        try {
            Long count = recipeMapper.countRecipes();
            return Result.success(count);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取今日新增菜谱数 (供admin-service调用)
     */
    @GetMapping("/count/today")
    public Result<Long> getTodayNewRecipeCount() {
        try {
            Long count = recipeMapper.countTodayNewRecipes();
            return Result.success(count);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取AI生成菜谱数 (供admin-service调用)
     */
    @GetMapping("/count/ai")
    public Result<Long> getAiRecipeCount() {
        try {
            Long count = recipeMapper.countAiRecipes();
            return Result.success(count);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取用户创建菜谱数 (供admin-service调用)
     */
    @GetMapping("/count/user")
    public Result<Long> getUserRecipeCount() {
        try {
            Long count = recipeMapper.countUserRecipes();
            return Result.success(count);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取收藏总数 (供admin-service调用)
     */
    @GetMapping("/favorites/count")
    public Result<Long> getFavoriteCount() {
        try {
            Long count = recipeMapper.countFavorites();
            return Result.success(count);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取今日新增收藏数 (供admin-service调用)
     */
    @GetMapping("/favorites/count/today")
    public Result<Long> getTodayNewFavoriteCount() {
        try {
            Long count = recipeMapper.countTodayNewFavorites();
            return Result.success(count);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取菜谱列表 (供admin-service调用)
     */
    @GetMapping("/list")
    public Result<PageResult<RecipeListResponse>> getRecipeList(
            @RequestParam("page") Integer page,
            @RequestParam("size") Integer size,
            @RequestParam(value = "keyword", required = false) String keyword) {
        try {
            Long currentPage = (long) page;
            Long pageSize = (long) size;

            // 使用现有的服务方法获取菜谱列表，传入null作为当前用户ID（管理员查看不需要收藏状态）
            IPage<RecipeListResponse> pageResult = recipeService.getRecipeList(currentPage, pageSize, keyword, null);

            PageResult<RecipeListResponse> result = new PageResult<>(
                pageResult.getRecords(),
                pageResult.getTotal(),
                pageResult.getCurrent(),
                pageResult.getSize()
            );

            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除菜谱 (供admin-service调用)
     */
    @DeleteMapping("/{recipeId}/admin")
    public Result<Void> deleteRecipeByAdmin(@PathVariable Long recipeId) {
        try {
            // 管理员删除，不需要检查用户权限
            recipeMapper.deleteById(recipeId);
            return Result.success(null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量删除菜谱 (供admin-service调用)
     */
    @DeleteMapping("/batch")
    public Result<Void> batchDeleteRecipes(@RequestBody Map<String, List<Long>> request) {
        try {
            List<Long> ids = request.get("ids");
            if (ids == null || ids.isEmpty()) {
                return Result.error("参数错误");
            }

            // 批量删除菜谱
            recipeMapper.deleteBatchIds(ids);
            return Result.success(null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

}
